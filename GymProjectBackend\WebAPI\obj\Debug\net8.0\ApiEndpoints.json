[{"ContainingType": "AuthController", "Method": "ChangeCompany", "RelativePath": "api/Auth/change-company", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AuthController+ChangeCompanyRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "ChangePassword", "RelativePath": "api/Auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AuthController+ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "CheckLoginBan", "RelativePath": "api/Auth/check-login-ban", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceInfo", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "CheckPasswordChangeRequired", "RelativePath": "api/Auth/check-password-change-required", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "CheckRegisterBan", "RelativePath": "api/Auth/check-register-ban", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "GetUserDevices", "RelativePath": "api/Auth/devices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AuthController+LoginRequestModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AuthController+RefreshTokenRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AuthController+RegisterRequestModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "RegisterMember", "RelativePath": "api/Auth/register-member", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AuthController+MemberRegisterRequestModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "RevokeAllDevices", "RelativePath": "api/Auth/revoke-all-devices", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AuthController", "Method": "RevokeDevice", "RelativePath": "api/Auth/revoke-device", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Entities.DTOs.RevokeDeviceRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "ClearAll", "RelativePath": "api/CacheAdmin/clear", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "ClearEntity", "RelativePath": "api/CacheAdmin/clear/entity/{tenantId}/{entityName}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.Int32", "IsRequired": true}, {"Name": "entityName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "ClearTenant", "RelativePath": "api/CacheAdmin/clear/tenant/{tenantId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetHealth", "RelativePath": "api/CacheAdmin/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetCacheItemDetail", "RelativePath": "api/CacheAdmin/item/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetAllKeys", "RelativePath": "api/CacheAdmin/keys", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetKeysByPattern", "RelativePath": "api/CacheAdmin/keys/pattern/{pattern}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pattern", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetPerformanceMetrics", "RelativePath": "api/CacheAdmin/performance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetCacheSize", "RelativePath": "api/CacheAdmin/size", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetStatistics", "RelativePath": "api/CacheAdmin/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetDetailedStatistics", "RelativePath": "api/CacheAdmin/statistics/detailed", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetTenantStatistics", "RelativePath": "api/CacheAdmin/statistics/tenant/{tenantId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetCacheDetails", "RelativePath": "api/CacheAdmin/tenant/{tenantId}/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetEntityCounts", "RelativePath": "api/CacheAdmin/tenant/{tenantId}/entities", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetEntityKeys", "RelativePath": "api/CacheAdmin/tenant/{tenantId}/entity/{entityName}/keys", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.Int32", "IsRequired": true}, {"Name": "entityName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetTenantKeys", "RelativePath": "api/CacheAdmin/tenant/{tenantId}/keys", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetTenantCacheSize", "RelativePath": "api/CacheAdmin/tenant/{tenantId}/size", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetActiveTenants", "RelativePath": "api/CacheAdmin/tenants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "GetAllTenantSizes", "RelativePath": "api/CacheAdmin/tenants/sizes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CacheAdminController", "Method": "TestCache", "RelativePath": "api/CacheAdmin/test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CityController", "Method": "GetAll", "RelativePath": "api/City/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyController", "Method": "Add", "RelativePath": "api/Company/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "company", "Type": "Entities.Concrete.Company", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyController", "Method": "Delete", "RelativePath": "api/Company/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyController", "Method": "GetActiveCompanies", "RelativePath": "api/Company/getactivecompanies", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyController", "Method": "GetAll", "RelativePath": "api/Company/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyController", "Method": "Update", "RelativePath": "api/Company/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "company", "Type": "Entities.Concrete.Company", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyAdressController", "Method": "Add", "RelativePath": "api/CompanyAdress/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyAdress", "Type": "Entities.Concrete.CompanyAdress", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyAdressController", "Method": "Delete", "RelativePath": "api/CompanyAdress/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyAdressController", "Method": "GetAll", "RelativePath": "api/CompanyAdress/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyAdressController", "Method": "GetCompanyAdressDetails", "RelativePath": "api/CompanyAdress/getcompanyadressdetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyAdressController", "Method": "Update", "RelativePath": "api/CompanyAdress/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyAdress", "Type": "Entities.Concrete.CompanyAdress", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "Add", "RelativePath": "api/CompanyExercises/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "exerciseAddDto", "Type": "Entities.DTOs.CompanyExerciseAddDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "Delete", "RelativePath": "api/CompanyExercises/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "GetAll", "RelativePath": "api/CompanyExercises/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "GetByCategory", "RelativePath": "api/CompanyExercises/getbycategory/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "GetById", "RelativePath": "api/CompanyExercises/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "GetCombined", "RelativePath": "api/CompanyExercises/getcombined", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "GetCombinedByCategory", "RelativePath": "api/CompanyExercises/getcombinedbycategory/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "GetCombinedFiltered", "RelativePath": "api/CompanyExercises/getcombinedfiltered", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "Entities.DTOs.SystemExerciseFilterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "GetDetail", "RelativePath": "api/CompanyExercises/getdetail/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "GetFiltered", "RelativePath": "api/CompanyExercises/getfiltered", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "Entities.DTOs.CompanyExerciseFilterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "Search", "RelativePath": "api/CompanyExercises/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyExercisesController", "Method": "Update", "RelativePath": "api/CompanyExercises/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "exerciseUpdateDto", "Type": "Entities.DTOs.CompanyExerciseUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "Add", "RelativePath": "api/CompanyUser/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyUser", "Type": "Core.Entities.Concrete.CompanyUser", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "Delete", "RelativePath": "api/CompanyUser/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "GetDeletedCompanyUsers", "RelativePath": "api/CompanyUser/get-deleted", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "GetAll", "RelativePath": "api/CompanyUser/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "GetByCityId", "RelativePath": "api/CompanyUser/getbycityid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "GetById", "RelativePath": "api/CompanyUser/getbyid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "Get", "RelativePath": "api/CompanyUser/getcompanydetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "GetCompanyUserDetails", "RelativePath": "api/CompanyUser/getcompanyuserdetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "GetCompanyUserDetailsByCityId", "RelativePath": "api/CompanyUser/getcompanyuserdetailsbycityid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "cityId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "GetCompanyUserFullDetails", "RelativePath": "api/CompanyUser/getfulldetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "GetCompanyUsersPaginated", "RelativePath": "api/CompanyUser/getpaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "RestoreCompanyUser", "RelativePath": "api/CompanyUser/restore", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "SoftDeleteCompanyUser", "RelativePath": "api/CompanyUser/soft-delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "Update", "RelativePath": "api/CompanyUser/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyUser", "Type": "Core.Entities.Concrete.CompanyUser", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.CompanyUserController", "Method": "UpdateCompanyUserFull", "RelativePath": "api/CompanyUser/updatefull", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "Entities.DTOs.CompanyUserFullUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.DebtPaymentsController", "Method": "Delete", "RelativePath": "api/DebtPayments/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExerciseCategoriesController", "Method": "Add", "RelativePath": "api/ExerciseCategories/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryAddDto", "Type": "Entities.DTOs.ExerciseCategoryAddDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExerciseCategoriesController", "Method": "Delete", "RelativePath": "api/ExerciseCategories/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExerciseCategoriesController", "Method": "GetActive", "RelativePath": "api/ExerciseCategories/getactive", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExerciseCategoriesController", "Method": "GetAll", "RelativePath": "api/ExerciseCategories/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExerciseCategoriesController", "Method": "GetById", "RelativePath": "api/ExerciseCategories/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExerciseCategoriesController", "Method": "Update", "RelativePath": "api/ExerciseCategories/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryUpdateDto", "Type": "Entities.DTOs.ExerciseCategoryUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExpensesController", "Method": "Add", "RelativePath": "api/Expenses/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "expense", "Type": "Entities.Concrete.Expense", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExpensesController", "Method": "Delete", "RelativePath": "api/Expenses/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExpensesController", "Method": "GetAll", "RelativePath": "api/Expenses/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExpensesController", "Method": "GetAllFiltered", "RelativePath": "api/Expenses/getallfiltered", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ExpenseType", "Type": "System.String", "IsRequired": false}, {"Name": "MinAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchText", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExpensesController", "Method": "GetAllPaginated", "RelativePath": "api/Expenses/getallpaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ExpenseType", "Type": "System.String", "IsRequired": false}, {"Name": "MinAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchText", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExpensesController", "Method": "GetByDateRange", "RelativePath": "api/Expenses/getbydaterange", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "endDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExpensesController", "Method": "GetById", "RelativePath": "api/Expenses/getbyid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExpensesController", "Method": "GetDashboardData", "RelativePath": "api/Expenses/getdashboarddata", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "year", "Type": "System.Int32", "IsRequired": false}, {"Name": "month", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExpensesController", "Method": "GetExpenseTotals", "RelativePath": "api/Expenses/getexpensetotals", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ExpenseType", "Type": "System.String", "IsRequired": false}, {"Name": "MinAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchText", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExpensesController", "Method": "GetMonthlyExpense", "RelativePath": "api/Expenses/getmonthlyexpense", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "year", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ExpensesController", "Method": "Update", "RelativePath": "api/Expenses/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "expense", "Type": "Entities.Concrete.Expense", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.LicensePackagesController", "Method": "Add", "RelativePath": "api/LicensePackages/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "licensePackage", "Type": "Entities.Concrete.LicensePackage", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.LicensePackagesController", "Method": "Delete", "RelativePath": "api/LicensePackages/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.LicensePackagesController", "Method": "GetAll", "RelativePath": "api/LicensePackages/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.LicensePackagesController", "Method": "GetById", "RelativePath": "api/LicensePackages/getbyid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.LicensePackagesController", "Method": "Update", "RelativePath": "api/LicensePackages/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "licensePackage", "Type": "Entities.Concrete.LicensePackage", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.LicenseTransactionsController", "Method": "Add", "RelativePath": "api/LicenseTransactions/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "licenseTransaction", "Type": "Entities.Concrete.LicenseTransaction", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.LicenseTransactionsController", "Method": "Delete", "RelativePath": "api/LicenseTransactions/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.LicenseTransactionsController", "Method": "GetAll", "RelativePath": "api/LicenseTransactions/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "startDate", "Type": "System.String", "IsRequired": false}, {"Name": "endDate", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.LicenseTransactionsController", "Method": "GetByUserId", "RelativePath": "api/LicenseTransactions/getbyuserid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.LicenseTransactionsController", "Method": "GetMonthlyRevenue", "RelativePath": "api/LicenseTransactions/getmonthlyrevenue", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "year", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.LicenseTransactionsController", "Method": "GetTotals", "RelativePath": "api/LicenseTransactions/gettotals", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "Add", "RelativePath": "api/Member/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "member", "Type": "Entities.Concrete.Member", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "Delete", "RelativePath": "api/Member/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetMemberProfile", "RelativePath": "api/Member/get-profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetActiveMemberCounts", "RelativePath": "api/Member/getactivemembercounts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetActiveMembers", "RelativePath": "api/Member/getactivemembers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetAll", "RelativePath": "api/Member/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetAllPaginated", "RelativePath": "api/Member/getallpaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchText", "Type": "System.String", "IsRequired": false}, {"Name": "gender", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetBranchCounts", "RelativePath": "api/Member/getbranchcounts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetByMemberId", "RelativePath": "api/Member/getbymemberid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetByPhoneNumber", "RelativePath": "api/Member/getbyphone", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "phoneNumber", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetMemberDetailById", "RelativePath": "api/Member/getmemberdetailbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetMemberDetails", "RelativePath": "api/Member/getmemberdetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetMemberDetailsPaginated", "RelativePath": "api/Member/getmemberdetailspaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Gender", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Branch", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SearchText", "Type": "System.String", "IsRequired": false}, {"Name": "MembershipTypeIds", "Type": "System.Int32[]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetMemberEntriesBySearch", "RelativePath": "api/Member/getmemberentriesbysearch", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchText", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetMemberEntryExitHistory", "RelativePath": "api/Member/getmemberentryexithistory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetMemberQRByUserId", "RelativePath": "api/Member/getmemberqrbyuserid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetMemberRemainingDay", "RelativePath": "api/Member/getmemberremainingday", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetMembersByMultiplePackages", "RelativePath": "api/Member/getmembersbymultiplepackages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "membershipTypeIds", "Type": "System.Int32[]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchText", "Type": "System.String", "IsRequired": false}, {"Name": "gender", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetMembersWithBalancePaginated", "RelativePath": "api/Member/getmemberswithbalancepaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchText", "Type": "System.String", "IsRequired": false}, {"Name": "balanceFilter", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetTodayEntries", "RelativePath": "api/Member/gettodayentries", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "date", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetTodayEntriesPaginated", "RelativePath": "api/Member/gettodayentriespaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "date", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "searchText", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetTotalActiveMembers", "RelativePath": "api/Member/gettotalactivemembers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetTotalRegisteredMembers", "RelativePath": "api/Member/gettotalregisteredmembers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "GetUpcomingBirthdays", "RelativePath": "api/Member/getupcomingbirthdays", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "days", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "ScanMember", "RelativePath": "api/Member/scannumber", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "scanMemberDto", "Type": "Entities.DTOs.ScanMemberForApiDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "Update", "RelativePath": "api/Member/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "member", "Type": "Entities.Concrete.Member", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberController", "Method": "UpdateProfile", "RelativePath": "api/Member/update-profile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "profileUpdateDto", "Type": "Entities.DTOs.MemberProfileUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "Add", "RelativePath": "api/Membership/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "membership", "Type": "Entities.DTOs.MembershipAddDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "CancelFreeze", "RelativePath": "api/Membership/cancel-freeze/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "Delete", "RelativePath": "api/Membership/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "DeleteMembershipById", "RelativePath": "api/Membership/deletemembershipbyid/{membershipId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "membershipId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "FreezeMembership", "RelativePath": "api/Membership/freeze", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "freezeRequest", "Type": "Entities.DTOs.MembershipFreezeRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "GetFrozenMemberships", "RelativePath": "api/Membership/frozen", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "GetAll", "RelativePath": "api/Membership/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "GetByMemberId", "RelativePath": "api/Membership/getbymembershipid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "GetLastMembershipInfo", "RelativePath": "api/Membership/getlastmembershipinfo/{memberId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "GetMemberActiveMemberships", "RelativePath": "api/Membership/getmemberactivememberships/{memberId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "ReactivateFromToday", "RelativePath": "api/Membership/reactivate-from-today/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "UnfreezeMembership", "RelativePath": "api/Membership/unfreeze/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipController", "Method": "Update", "RelativePath": "api/Membership/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "membership", "Type": "Entities.DTOs.MembershipUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipFreezeHistoryController", "Method": "Add", "RelativePath": "api/MembershipFreezeHistory/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "history", "Type": "Entities.Concrete.MembershipFreezeHistory", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipFreezeHistoryController", "Method": "GetAll", "RelativePath": "api/MembershipFreezeHistory/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipFreezeHistoryController", "Method": "GetByMembershipId", "RelativePath": "api/MembershipFreezeHistory/getbymembershipid/{membershipId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "membershipId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipFreezeHistoryController", "Method": "GetRemainingFreezeDays", "RelativePath": "api/MembershipFreezeHistory/getremainingfreezedays/{membershipId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "membershipId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipFreezeHistoryController", "Method": "Update", "RelativePath": "api/MembershipFreezeHistory/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "history", "Type": "Entities.Concrete.MembershipFreezeHistory", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipTypeController", "Method": "Add", "RelativePath": "api/MembershipType/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "membershipType", "Type": "Entities.Concrete.MembershipType", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipTypeController", "Method": "Delete", "RelativePath": "api/MembershipType/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipTypeController", "Method": "GetAll", "RelativePath": "api/MembershipType/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipTypeController", "Method": "GetBranchesAndTypes", "RelativePath": "api/MembershipType/getallbranches", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipTypeController", "Method": "GetAllPaginated", "RelativePath": "api/MembershipType/getallpaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SearchText", "Type": "System.String", "IsRequired": false}, {"Name": "Branch", "Type": "System.String", "IsRequired": false}, {"Name": "MinPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinDuration", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxDuration", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipTypeController", "Method": "GetPackagesByBranch", "RelativePath": "api/MembershipType/getpackagesbybranch", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "branch", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MembershipTypeController", "Method": "Update", "RelativePath": "api/MembershipType/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "membershipType", "Type": "Entities.Concrete.MembershipType", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberWorkoutProgramController", "Method": "AssignProgram", "RelativePath": "api/MemberWorkoutProgram/assign", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "assignmentDto", "Type": "Entities.DTOs.MemberWorkoutProgramAddDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberWorkoutProgramController", "Method": "DeleteAssignment", "RelativePath": "api/MemberWorkoutProgram/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberWorkoutProgramController", "Method": "GetActiveAssignmentCount", "RelativePath": "api/MemberWorkoutProgram/getactiveassignmentcount", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberWorkoutProgramController", "Method": "GetActiveWorkoutProgramsByUserId", "RelativePath": "api/MemberWorkoutProgram/getactiveprogramsbyuser", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberWorkoutProgramController", "Method": "GetAssignedMemberCount", "RelativePath": "api/MemberWorkoutProgram/getassignedmembercount", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workoutProgramTemplateId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberWorkoutProgramController", "Method": "GetAssignmentDetail", "RelativePath": "api/MemberWorkoutProgram/getassignmentdetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberWorkoutProgramController", "Method": "GetCompanyAssignments", "RelativePath": "api/MemberWorkoutProgram/getcompanyassignments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberWorkoutProgramController", "Method": "GetMemberActivePrograms", "RelativePath": "api/MemberWorkoutProgram/getmemberactiveprograms", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberWorkoutProgramController", "Method": "GetMemberProgramHistory", "RelativePath": "api/MemberWorkoutProgram/getmemberprogramhistory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberWorkoutProgramController", "Method": "GetProgramDetailByUser", "RelativePath": "api/MemberWorkoutProgram/getprogramdetailbyuser", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberWorkoutProgramId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.MemberWorkoutProgramController", "Method": "UpdateAssignment", "RelativePath": "api/MemberWorkoutProgram/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "assignmentDto", "Type": "Entities.DTOs.MemberWorkoutProgramUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.OperationClaimsController", "Method": "Add", "RelativePath": "api/OperationClaims/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "operationClaim", "Type": "Core.Entities.Concrete.OperationClaim", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.OperationClaimsController", "Method": "Delete", "RelativePath": "api/OperationClaims/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.OperationClaimsController", "Method": "GetAll", "RelativePath": "api/OperationClaims/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.OperationClaimsController", "Method": "GetById", "RelativePath": "api/OperationClaims/getbyid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.OperationClaimsController", "Method": "Update", "RelativePath": "api/OperationClaims/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "operationClaim", "Type": "Core.Entities.Concrete.OperationClaim", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.PaymentController", "Method": "Add", "RelativePath": "api/Payment/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "payment", "Type": "Entities.Concrete.Payment", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.PaymentController", "Method": "Delete", "RelativePath": "api/Payment/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.PaymentController", "Method": "GetAll", "RelativePath": "api/Payment/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.PaymentController", "Method": "GetAllPaymentHistoryFiltered", "RelativePath": "api/Payment/getallpaymenthistoryfiltered", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PaymentMethod", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchText", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.PaymentController", "Method": "GetDebtorMembers", "RelativePath": "api/Payment/GetDebtorMembers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.PaymentController", "Method": "GetMonthlyRevenue", "RelativePath": "api/Payment/getmonthlyrevenue", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "year", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.PaymentController", "Method": "GetPaymentHistory", "RelativePath": "api/Payment/getpaymenthistory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.PaymentController", "Method": "GetPaymentHistoryPaginated", "RelativePath": "api/Payment/getpaymenthistorypaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PaymentMethod", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchText", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.PaymentController", "Method": "GetPaymentTotals", "RelativePath": "api/Payment/getpaymenttotals", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PaymentMethod", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchText", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.PaymentController", "Method": "Update", "RelativePath": "api/Payment/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "payment", "Type": "Entities.Concrete.Payment", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.PaymentController", "Method": "UpdatePaymentStatus", "RelativePath": "api/Payment/updatestatus/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateDto", "Type": "Entities.DTOs.PaymentUpdateModelDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ProductsController", "Method": "Add", "RelativePath": "api/Products/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "product", "Type": "Entities.Concrete.Product", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ProductsController", "Method": "Delete", "RelativePath": "api/Products/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ProductsController", "Method": "GetAll", "RelativePath": "api/Products/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ProductsController", "Method": "GetAllPaginated", "RelativePath": "api/Products/getallpaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchText", "Type": "System.String", "IsRequired": false}, {"Name": "minPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maxPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ProductsController", "Method": "GetById", "RelativePath": "api/Products/getbyid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.ProductsController", "Method": "Update", "RelativePath": "api/Products/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "product", "Type": "Entities.Concrete.Product", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.RemainingDebtsController", "Method": "AddDebtPayment", "RelativePath": "api/RemainingDebts/adddebtpayment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "debtPaymentDto", "Type": "Entities.DTOs.DebtPaymentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.RemainingDebtsController", "Method": "Delete", "RelativePath": "api/RemainingDebts/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.RemainingDebtsController", "Method": "GetRemainingDebtDetails", "RelativePath": "api/RemainingDebts/getremainingdebtdetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.SystemExercisesController", "Method": "Add", "RelativePath": "api/SystemExercises/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "exerciseAddDto", "Type": "Entities.DTOs.SystemExerciseAddDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.SystemExercisesController", "Method": "Delete", "RelativePath": "api/SystemExercises/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.SystemExercisesController", "Method": "GetAll", "RelativePath": "api/SystemExercises/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.SystemExercisesController", "Method": "GetByCategory", "RelativePath": "api/SystemExercises/getbycategory/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.SystemExercisesController", "Method": "GetById", "RelativePath": "api/SystemExercises/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.SystemExercisesController", "Method": "GetDetail", "RelativePath": "api/SystemExercises/getdetail/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.SystemExercisesController", "Method": "GetFiltered", "RelativePath": "api/SystemExercises/getfiltered", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "Entities.DTOs.SystemExerciseFilterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.SystemExercisesController", "Method": "Search", "RelativePath": "api/SystemExercises/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.SystemExercisesController", "Method": "Update", "RelativePath": "api/SystemExercises/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "exerciseUpdateDto", "Type": "Entities.DTOs.SystemExerciseUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TownController", "Method": "GetAll", "RelativePath": "api/Town/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TownController", "Method": "GetByCityId", "RelativePath": "api/Town/getbycityid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TransactionsController", "Method": "Add", "RelativePath": "api/Transactions/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "transaction", "Type": "Entities.Concrete.Transaction", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TransactionsController", "Method": "AddBulk", "RelativePath": "api/Transactions/addBulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "bulkTransaction", "Type": "Entities.DTOs.BulkTransactionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TransactionsController", "Method": "Delete", "RelativePath": "api/Transactions/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TransactionsController", "Method": "GetAll", "RelativePath": "api/Transactions/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TransactionsController", "Method": "GetByMemberId", "RelativePath": "api/Transactions/getbymemberid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TransactionsController", "Method": "GetDailyTransactionTotal", "RelativePath": "api/Transactions/getdailytotal", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "date", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TransactionsController", "Method": "GetMonthlyTransactionTotal", "RelativePath": "api/Transactions/getmonthlytotal", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "year", "Type": "System.Int32", "IsRequired": false}, {"Name": "month", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TransactionsController", "Method": "GetUnpaidTransactions", "RelativePath": "api/Transactions/getunpaidtransactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TransactionsController", "Method": "GetWithUserProductDetails", "RelativePath": "api/Transactions/getwithuserproductdetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TransactionsController", "Method": "UpdateAllPaymentStatus", "RelativePath": "api/Transactions/updateallpaymentstatus/{memberId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.TransactionsController", "Method": "UpdatePaymentStatus", "RelativePath": "api/Transactions/updatepaymentstatus/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UnifiedCompanyController", "Method": "Add", "RelativePath": "api/UnifiedCompany/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "unifiedCompanyDto", "Type": "Entities.DTOs.UnifiedCompanyAddDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "DeleteProfileImage", "RelativePath": "api/User/delete-profile-image", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "GetAll", "RelativePath": "api/User/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "GetNonMembers", "RelativePath": "api/User/getnonmembers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "GetNonMembersCount", "RelativePath": "api/User/getnonmemberscount", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "GetNonMembersPaginated", "RelativePath": "api/User/getnonmemberspaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "Health", "RelativePath": "api/User/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "GetUserProfile", "RelativePath": "api/User/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "GetProfileImage", "RelativePath": "api/User/profile-image/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "RecordFileDownload", "RelativePath": "api/User/record-file-download", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "GetRemainingFileDownloads", "RelativePath": "api/User/remaining-file-downloads", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "GetRemainingProfileImageUploads", "RelativePath": "api/User/remaining-profile-image-uploads", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserController", "Method": "UploadProfileImage", "RelativePath": "api/User/upload-profile-image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserCompanyController", "Method": "Add", "RelativePath": "api/UserCompany/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userCompany", "Type": "Entities.Concrete.UserCompany", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserCompanyController", "Method": "Delete", "RelativePath": "api/UserCompany/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserCompanyController", "Method": "GetAll", "RelativePath": "api/UserCompany/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserCompanyController", "Method": "GetUserCompanyDetails", "RelativePath": "api/UserCompany/getusercompanydetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserCompanyController", "Method": "Update", "RelativePath": "api/UserCompany/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userCompany", "Type": "Entities.Concrete.UserCompany", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "Add", "RelativePath": "api/UserLicenses/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userLicense", "Type": "Entities.Concrete.UserLicense", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "Delete", "RelativePath": "api/UserLicenses/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "ExtendLicenseByPackage", "RelativePath": "api/UserLicenses/extendbypackage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "licenseExtensionByPackageDto", "Type": "Entities.DTOs.LicenseExtensionByPackageDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "GetActiveByUserId", "RelativePath": "api/UserLicenses/getactivebyuserid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "GetAll", "RelativePath": "api/UserLicenses/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "GetAllPaginated", "RelativePath": "api/UserLicenses/getallpaginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "companyName", "Type": "System.String", "IsRequired": false}, {"Name": "remainingDaysMin", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "remainingDaysMax", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "GetById", "RelativePath": "api/UserLicenses/getbyid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "GetExpiredAndPassive", "RelativePath": "api/UserLicenses/getexpiredandpassive", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "GetMyActiveLicenses", "RelativePath": "api/UserLicenses/getmyactivelicenses", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "GetUserRoles", "RelativePath": "api/UserLicenses/getuserroles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "Purchase", "RelativePath": "api/UserLicenses/purchase", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "licensePurchaseDto", "Type": "Entities.DTOs.LicensePurchaseDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "RevokeLicense", "RelativePath": "api/UserLicenses/revoke", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userLicenseId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserLicensesController", "Method": "Update", "RelativePath": "api/UserLicenses/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userLicense", "Type": "Entities.Concrete.UserLicense", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserOperationClaimsController", "Method": "Add", "RelativePath": "api/UserOperationClaims/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userOperationClaim", "Type": "Core.Entities.Concrete.UserOperationClaim", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserOperationClaimsController", "Method": "Delete", "RelativePath": "api/UserOperationClaims/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserOperationClaimsController", "Method": "GetAll", "RelativePath": "api/UserOperationClaims/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserOperationClaimsController", "Method": "GetById", "RelativePath": "api/UserOperationClaims/getbyid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserOperationClaimsController", "Method": "GetUserOperationClaimDetails", "RelativePath": "api/UserOperationClaims/getuseroperationclaimdetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.UserOperationClaimsController", "Method": "Update", "RelativePath": "api/UserOperationClaims/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userOperationClaim", "Type": "Core.Entities.Concrete.UserOperationClaim", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.WorkoutProgramController", "Method": "Add", "RelativePath": "api/WorkoutProgram/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateAddDto", "Type": "Entities.DTOs.WorkoutProgramTemplateAddDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.WorkoutProgramController", "Method": "Delete", "RelativePath": "api/WorkoutProgram/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.WorkoutProgramController", "Method": "GetAll", "RelativePath": "api/WorkoutProgram/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.WorkoutProgramController", "Method": "GetById", "RelativePath": "api/WorkoutProgram/getbyid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebAPI.Controllers.WorkoutProgramController", "Method": "Update", "RelativePath": "api/WorkoutProgram/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateUpdateDto", "Type": "Entities.DTOs.WorkoutProgramTemplateUpdateDto", "IsRequired": true}], "ReturnTypes": []}]