/* Loading spinner artık generic component'te yönetiliyor */

/* Date Range Indicator - Full Width */
.date-range-indicator-full {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
  transition: all 0.3s ease;
  width: 100%;
}

.date-range-indicator-full:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(var(--primary-rgb), 0.4);
}

.date-range-text {
  font-size: 0.95rem;
  letter-spacing: 0.3px;
}

/* Year Filter Dropdown */
.year-filter .form-select {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.year-filter .form-select:focus {
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
  background-color: rgba(255, 255, 255, 0.15);
}

.year-filter .form-select option {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
  transition: filter 0.3s ease;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Card Styles */
.card {
  border-radius: var(--border-radius-lg);
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1.25rem 1.5rem;
  background: linear-gradient(to right, var(--bg-secondary), var(--bg-primary));
}

.card-body {
  padding: 1.75rem;
}

/* Chart Container */
.chart-container {
  position: relative;
  margin: auto;
  border-radius: var(--border-radius-md);
  padding: 1rem;
  background-color: var(--bg-secondary);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.1);
}

/* Total Cards */
.total-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border-radius: var(--border-radius-lg);
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.total-card:hover {
  transform: translateY(-7px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.total-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
  pointer-events: none;
}

.total-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  margin-right: 1.25rem;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.total-card:hover .total-icon {
  transform: scale(1.1);
}

.total-info {
  flex-grow: 1;
  text-align: left;
  position: relative;
  z-index: 1;
}

.total-info h3 {
  margin-bottom: 0.25rem;
  font-size: 1.75rem;
  font-weight: 700;
  transition: all 0.3s ease;
}

.total-card:hover .total-info h3 {
  transform: scale(1.05);
}

.total-info p {
  margin-bottom: 0;
  opacity: 0.9;
  font-weight: 600;
  font-size: 1.1rem;
}

.total-cash {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.2) 100%);
  color: var(--success);
}

.total-cash .total-icon {
  background: linear-gradient(135deg, var(--success) 0%, #218838 100%);
  color: white;
}

.total-credit-card {
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.2) 100%);
  color: var(--primary);
}

.total-credit-card .total-icon {
  background: linear-gradient(135deg, var(--primary) 0%, #0056b3 100%);
  color: white;
}

.total-transfer {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.2) 100%);
  color: var(--warning);
}

.total-transfer .total-icon {
  background: linear-gradient(135deg, var(--warning) 0%, #d39e00 100%);
  color: white;
}

.total-all {
  background: linear-gradient(135deg, rgba(108, 117, 125, 0.1) 0%, rgba(108, 117, 125, 0.2) 100%);
  color: var(--secondary);
}

.total-all .total-icon {
  background: linear-gradient(135deg, var(--secondary) 0%, #5a6268 100%);
  color: white;
}

/* Payment Method Badges */
.payment-method-badge {
  display: inline-block;
  padding: 0.4rem 0.85rem;
  border-radius: var(--border-radius-pill);
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.payment-method-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.payment-cash {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.2) 100%);
  color: var(--success);
}

.payment-credit-card {
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.2) 100%);
  color: var(--primary);
}

.payment-transfer {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.2) 100%);
  color: var(--warning);
}

.payment-debt {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.2) 100%);
  color: var(--danger);
}

/* Avatar */
.avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  flex-shrink: 0;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.avatar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
  pointer-events: none;
}

.avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.2);
}

/* Table Styles */
.table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

.table th {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.table td {
  vertical-align: middle;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.table tbody tr {
  transition: all 0.3s ease;
  position: relative;
}

.table tbody tr::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background-color: var(--primary-light);
  z-index: -1;
  transition: width 0.3s ease;
}

.table tbody tr:hover::after {
  width: 100%;
}

.table tbody tr:hover td {
  transform: translateX(5px);
}

/* Pagination */
.pagination {
  margin-bottom: 0;
  display: flex;
  gap: 0.35rem;
}

.pagination .page-link {
  border: none;
  color: var(--text-secondary);
  padding: 0.6rem 0.9rem;
  margin: 0;
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.pagination .page-link:hover {
  background-color: var(--primary-light);
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pagination .page-item.active .page-link {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  box-shadow: 0 4px 10px rgba(0, 123, 255, 0.3);
}

.pagination .page-item.disabled .page-link {
  color: var(--text-secondary);
  opacity: 0.5;
  box-shadow: none;
}

/* Modern Pagination Styles */
.modern-pagination {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: 0.25rem;
}

.modern-page-item {
  display: flex;
}

.modern-page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.6rem 0.9rem;
  min-width: 2.5rem;
  height: 2.5rem;
  border: none;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.modern-page-link:hover {
  background-color: var(--primary-light);
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

.modern-page-item.active .modern-page-link {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.3);
}

.modern-page-item.disabled .modern-page-link {
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
  opacity: 0.5;
  box-shadow: none;
  cursor: not-allowed;
  transform: none;
}

.modern-page-item.disabled .modern-page-link:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  transform: none;
  box-shadow: none;
}

/* Dark mode specific adjustments for modern pagination */
[data-theme="dark"] .modern-page-link {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .modern-page-link:hover {
  background-color: var(--primary-light);
  color: var(--primary);
  border-color: var(--primary);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .modern-page-item.active .modern-page-link {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  border-color: var(--primary);
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.4);
}

[data-theme="dark"] .modern-page-item.disabled .modern-page-link {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
  opacity: 0.4;
}

/* Empty State */
.empty-state {
  padding: 3rem 2rem;
  text-align: center;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  margin: 1rem 0;
  transition: all 0.3s ease;
}

.empty-state i {
  font-size: 3.5rem;
  color: var(--text-secondary);
  opacity: 0.7;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.empty-state:hover i {
  transform: scale(1.1);
  opacity: 0.9;
}

.empty-state h5 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.empty-state p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  max-width: 80%;
  margin: 0 auto 1.5rem;
}

.empty-state .btn {
  transition: all 0.3s ease;
  padding: 0.6rem 1.5rem;
  font-weight: 500;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.empty-state .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

/* Active Filters */
.active-filters {
  margin-bottom: 1.5rem;
}

.filter-badge {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.2) 100%);
  color: var(--primary);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-pill);
  font-size: 0.9rem;
  font-weight: 500;
  margin-right: 0.75rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.filter-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.1);
}

.filter-badge .btn-close {
  font-size: 0.7rem;
  padding: 0.3rem;
  margin-left: 0.5rem;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.filter-badge .btn-close:hover {
  background-color: rgba(255, 255, 255, 0.5);
  transform: rotate(90deg);
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .card-body {
    padding: 1.25rem;
  }
  
  .total-card {
    padding: 1.25rem;
    margin-bottom: 1rem;
  }
  
  .total-icon {
    width: 55px;
    height: 55px;
    font-size: 1.35rem;
  }
  
  .total-info h3 {
    font-size: 1.35rem;
  }
  
  .pagination-info {
    display: none;
  }
  
  .table th, .table td {
    padding: 0.75rem;
  }
  
  .filter-badge {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
  }
}

/* Tablet Adjustments */
@media (min-width: 768px) and (max-width: 991.98px) {
  .card-body {
    padding: 1.5rem;
  }
  
  .total-card {
    padding: 1.35rem;
  }
  
  .total-icon {
    width: 60px;
    height: 60px;
  }
}

/* Dark Mode Support */
[data-theme="dark"] .card {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

/* Card header styling for both light and dark modes */
.card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1.25rem 1.5rem;
  background: linear-gradient(to right, var(--bg-secondary), var(--bg-primary));
}

.card-header h5, h5 {
  color: #00b3ff !important; /* Bright blue color visible in both modes */
  font-weight: bold;
}

[data-theme="dark"] .table {
  color: var(--text-primary);
}

[data-theme="dark"] .table thead th {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .table tbody tr::after {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .loading-overlay {
  background-color: rgba(18, 18, 18, 0.8);
}

[data-theme="dark"] .content-blur {
  filter: blur(3px) brightness(0.7);
}

[data-theme="dark"] .pagination .page-link {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .pagination .page-link:hover {
  background-color: var(--bg-tertiary);
  filter: brightness(1.2);
}

[data-theme="dark"] .pagination .page-item.active .page-link {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
}

[data-theme="dark"] .pagination .page-item.disabled .page-link {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  opacity: 0.5;
}

[data-theme="dark"] .text-muted {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .empty-state {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .chart-container {
  background-color: var(--bg-tertiary);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .total-card::after,
[data-theme="dark"] .avatar::after {
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
}

/* Fix for total values text in dark mode */
[data-theme="dark"] .total-info h3,
[data-theme="dark"] .total-info p {
  color: white !important;
}

/* Form controls and labels styling for both light and dark modes */
.form-label {
  color: #888888 !important; /* Gray color visible in both modes */
  font-weight: bold;
}

input::placeholder,
textarea::placeholder,
.form-control::placeholder {
  color: #888888 !important; /* Gray color visible in both modes */
  opacity: 0.9 !important;
}

/* Ensure form controls have proper contrast in dark mode */
[data-theme="dark"] .form-control {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .input-group-text {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

/* Ensure mat-autocomplete options are visible in dark mode */
[data-theme="dark"] .mat-option {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .mat-option:hover:not(.mat-option-disabled) {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Animation Effects */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes zoomIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.card {
  animation: zoomIn 0.5s ease-out;
}

.total-card {
  animation: slideInLeft 0.5s ease-out;
}

.chart-container {
  animation: fadeIn 0.8s ease-out;
}

.table tbody tr {
  animation: slideInRight 0.3s ease-out;
  animation-fill-mode: both;
}

.table tbody tr:nth-child(1) { animation-delay: 0.05s; }
.table tbody tr:nth-child(2) { animation-delay: 0.1s; }
.table tbody tr:nth-child(3) { animation-delay: 0.15s; }
