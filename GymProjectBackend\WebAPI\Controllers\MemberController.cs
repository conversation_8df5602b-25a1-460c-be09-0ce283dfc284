
using Business.Abstract;
using Core.Entities.Concrete;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using System.Linq;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class MemberController : ControllerBase
    {
        IMemberService _memberService;

        public MemberController(IMemberService memberService)
        {
            _memberService = memberService;
        }
        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _memberService.GetAll();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpPost("add")]
        public IActionResult Add(Member member)
        {
            var result = _memberService.Add(member);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }
        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _memberService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpPost("update")]
        public IActionResult Update(Member member)
        {
            var result = _memberService.Update(member);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }
        [HttpGet("getmemberdetails")]
        public IActionResult GetMemberDetails()
        {
            var result = _memberService.GetMemberDetails();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getactivemembers")]
        public IActionResult GetActiveMembers()
        {
            var result = _memberService.GetActiveMembers();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getmemberentryexithistory")]
        public IActionResult GetMemberEntryExitHistory()
        {
            var result = _memberService.GetMemberEntryExitHistory();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getmemberremainingday")]
        public IActionResult GetMemberRemainingDay()
        {
            var result = _memberService.GetMemberRemainingDay();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpPost("scannumber")]
        [AllowAnonymous]
        public IActionResult ScanMember(ScanMemberForApiDto scanMemberDto)
        {
            var result = _memberService.GetMemberRemainingDaysForScanNumber(scanMemberDto.ScanNumber);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getbymemberid")]
        public IActionResult GetByMemberId(int id)
        {
            var result = _memberService.GetByMemberId(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getbyphone")]
        public IActionResult GetByPhoneNumber(string phoneNumber)
        {
            var result = _memberService.GetMemberQRByPhoneNumber(phoneNumber);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("gettodayentries")]
        public IActionResult GetTodayEntries(DateTime date)
        {
            var result = _memberService.GetTodayEntries(date);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("gettodayentriespaginated")]
        public IActionResult GetTodayEntriesPaginated([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 25, [FromQuery] DateTime? date = null, [FromQuery] string searchText = "")
        {
            var parameters = new MemberEntryPagingParameters
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                Date = date ?? DateTime.Today,
                SearchText = searchText ?? ""
            };

            var result = _memberService.GetTodayEntriesPaginated(parameters);
            return result.Success ? Ok(result) : BadRequest(result);
        }
        [HttpGet("getallpaginated")]
        // gender query parametresi eklendi (nullable int)
        public IActionResult GetAllPaginated([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string searchText = "", [FromQuery] int? gender = null)
        {
            var parameters = new MemberPagingParameters
            {
                PageNumber = pageNumber,
                PageSize = pageSize,  // Dinamik pageSize
                SearchText = searchText,
                Gender = gender // Gelen gender parametresi atandı
            };

            var result = _memberService.GetAllPaginated(parameters);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getmemberswithbalancepaginated")]
        public IActionResult GetMembersWithBalancePaginated([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string searchText = "", [FromQuery] string balanceFilter = "all")
        {
            var parameters = new MemberPagingParameters
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                SearchText = searchText
            };

            var result = _memberService.GetMembersWithBalancePaginated(parameters, balanceFilter);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getmemberdetailspaginated")]
        public IActionResult GetMemberDetailsPaginated([FromQuery] MemberPagingParameters parameters)
        {
            var result = _memberService.GetMemberDetailsPaginated(parameters);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getmembersbymultiplepackages")]
        public IActionResult GetMembersByMultiplePackages([FromQuery] int[] membershipTypeIds, [FromQuery] int pageNumber = 1, [FromQuery] string searchText = "", [FromQuery] int? gender = null)
        {
            var parameters = new MemberPagingParameters
            {
                PageNumber = pageNumber,
                PageSize = 20,
                SearchText = searchText,
                MembershipTypeIds = membershipTypeIds,
                Gender = gender // Cinsiyet parametresi eklendi
            };

            var result = _memberService.GetMembersByMultiplePackages(parameters);
            return result.Success ? Ok(result) : BadRequest(result);
        }
        [HttpGet("gettotalactivemembers")]
        public IActionResult GetTotalActiveMembers()
        {
            var result = _memberService.GetTotalActiveMembers();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("gettotalregisteredmembers")]
        public IActionResult GetTotalRegisteredMembers()
        {
            var result = _memberService.GetTotalRegisteredMembers();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getactivemembercounts")]
        public IActionResult GetActiveMemberCounts()
        {
            var result = _memberService.GetActiveMemberCounts();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getbranchcounts")]
        public IActionResult GetBranchCounts()
        {
            var result = _memberService.GetBranchCounts();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getmemberentriesbysearch")]
        public IActionResult GetMemberEntriesBySearch(string searchText)
        {
            var result = _memberService.GetMemberEntriesByName(searchText);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getmemberdetailbyid/{id}")]
        public IActionResult GetMemberDetailById(int id)
        {
            var result = _memberService.GetMemberDetailById(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getupcomingbirthdays")]
        public IActionResult GetUpcomingBirthdays(int days = 3)
        {
            var result = _memberService.GetUpcomingBirthdays(days);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getmemberqrbyuserid")]
        [Authorize(Roles = "member")]
        public IActionResult GetMemberQRByUserId()
        {
            // Kullanıcının ID'sini token'dan al
            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest("Kullanıcı kimliği bulunamadı.");
            }

            var result = _memberService.GetMemberQRByUserIdWithoutCompanyFilter(userId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Üyenin kendi profil bilgilerini getirir
        /// Sadece member rolüne sahip kullanıcılar erişebilir
        /// </summary>
        [HttpGet("get-profile")]
        [Authorize(Roles = "member")]
        public IActionResult GetMemberProfile()
        {
            // Kullanıcının ID'sini token'dan al
            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest(new { success = false, message = "Kullanıcı kimliği bulunamadı." });
            }

            var result = _memberService.GetMemberProfileByUserId(userId);
            if (result.Success)
            {
                return Ok(new { success = true, message = result.Message, data = result.Data });
            }
            return BadRequest(new { success = false, message = result.Message });
        }

        /// <summary>
        /// Üyenin kendi profil bilgilerini günceller
        /// Sadece member rolüne sahip kullanıcılar erişebilir
        /// Güncellenebilir alanlar: FirstName, LastName, Adress, BirthDate
        /// </summary>
        [HttpPost("update-profile")]
        [Authorize(Roles = "member")]
        public IActionResult UpdateProfile([FromBody] MemberProfileUpdateDto profileUpdateDto)
        {
            // Kullanıcının ID'sini token'dan al
            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest(new { success = false, message = "Kullanıcı kimliği bulunamadı." });
            }

            // Model validation
            if (profileUpdateDto == null)
            {
                return BadRequest(new { success = false, message = "Profil bilgileri boş olamaz." });
            }

            var result = _memberService.UpdateMemberProfile(userId, profileUpdateDto);
            if (result.Success)
            {
                return Ok(new { success = true, message = result.Message });
            }
            return BadRequest(new { success = false, message = result.Message });
        }
    }
}
