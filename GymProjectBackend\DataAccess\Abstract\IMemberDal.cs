﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using MemberFilter = Entities.DTOs.MemberFilter;

namespace DataAccess.Abstract
{
    public interface IMemberDal:IEntityRepository<Member>
    {
        MemberDetailWithHistoryDto GetMemberDetailById(int memberId);
        List<MemberBirthdayDto> GetUpcomingBirthdays(int days);
        List<MembeFilterDto> GetMemberDetails();
        List<MemberEntryExitHistoryDto> GetMemberEntryExitHistory();
        List<MemberRemainingDayDto> GetMemberRemainingDay();
        Member GetMemberByScanNumber(string scanNumber);
        List<GetActiveMemberDto> GetActiveMembers();
        GetMemberQRByPhoneNumberDto GetMemberQRByPhoneNumber(string phoneNumber);
        List<MemberEntryDto> GetTodayEntries(DateTime date);
        PaginatedResult<MemberEntryDto> GetTodayEntriesPaginated(MemberEntryPagingParameters parameters);
        PaginatedResult<Member> GetAllPaginated(MemberPagingParameters parameters);
        PaginatedResult<MemberFilter> GetMemberDetailsPaginated(MemberPagingParameters parameters);
        PaginatedResult<Member> GetMembersWithBalancePaginated(MemberPagingParameters parameters, string balanceFilter);
        PaginatedResult<MemberFilter> GetMembersByMultiplePackages(MemberPagingParameters parameters);
        List<MemberEntryDto> GetMemberEntriesByName(string name);
        IDataResult<Dictionary<string, int>> GetBranchCounts(int companyId);
        IDataResult<int> GetTotalActiveMembers(int companyId);
        IDataResult<int> GetTotalRegisteredMembers(int companyId);
        IDataResult<Dictionary<string, int>> GetActiveMemberCounts(int companyId);
        IDataResult<MemberProfileDto> GetMemberProfileByUserId(int userId, int companyId);
        IResult UpdateMemberProfile(int userId, int companyId, MemberProfileUpdateDto profileUpdateDto);
        IDataResult<GetMemberQRByPhoneNumberDto> GetMemberQRByUserIdWithoutCompanyFilter(int userId);
        IDataResult<GetMemberQRByPhoneNumberDto> GetMemberQRByPhoneNumber(string phoneNumber, int companyId);
        IDataResult<MemberDetailDto> GetMemberRemainingDaysForScanNumber(string scanNumber, int companyId);

        // SOLID prensiplerine uygun refactoring için eklenen metotlar
        IResult AddMemberWithUserManagement(Member member, int companyId);
        IResult DeleteMemberWithUserManagement(int memberId, int companyId);
        IDataResult<MemberDetailWithHistoryDto> GetMemberDetailByIdWithCalculations(int memberId, int companyId);
        IResult UpdateMemberWithUserManagement(Member member, int companyId);
    }
}
